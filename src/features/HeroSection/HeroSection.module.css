/* src/features/HeroSection/HeroSection.module.css */
.heroSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rem 5rem 8rem 5rem; 
  max-width: 1400px;
  margin: 0 auto;
  background-color: #0A0A0A;

  clip-path: polygon(0 0, 100% 0, 100% 90%, 0 100%);
}

.content {
  width: 50%;
}

.promoBanner {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem; 
  margin-bottom: 1rem;
}

.headline {
  font-size: 4.5rem; 
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: white;
}

.subheadline {
  font-size: 1.125rem; 
  color: #A0A0A0; 
  margin-bottom: 2rem;
  max-width: 480px;
  line-height: 1.6;
}

.ctaForm {
  display: flex;
  align-items: center;
}

.ctaForm input {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border-radius: 6px;
  padding: 0.75rem;
  flex-grow: 1;
  font-size: 1rem;
}

.ctaForm input::placeholder {
  color: #A0A0A0;
}

.ctaForm button {
  background-color: white;
  color: black;
  font-weight: 700;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  margin-left: 0.5rem;
}

.animationContainer {
  width: 50%;
}