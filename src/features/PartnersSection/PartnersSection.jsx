// src/features/PartnersSection/PartnersSection.jsx
import React from 'react';
import styles from './PartnersSection.module.css';

// Import all the logos from the assets folder
import openAiLogo from '../../assets/logos/openai.svg';
import amazonLogo from '../../assets/logos/amazon.svg';
import googleLogo from '../../assets/logos/google.svg';
import anthropicLogo from '../../assets/logos/anthropic.svg';
import marriottLogo from '../../assets/logos/marriott.svg';
import shopifyLogo from '../../assets/logos/shopify.svg';
import airbnbLogo from '../../assets/logos/airbnb.svg';
import urbanOutfittersLogo from '../../assets/logos/urbanoutfitters.svg';

const logos = [
  { src: openAiLogo, alt: 'OpenAI' },
  { src: amazonLogo, alt: 'Amazon' },
  { src: googleLogo, alt: 'Google' },
  { src: anthropicLogo, alt: 'Anthropic' },
  { src: marriottLogo, alt: 'Marriott' },
  { src: shopifyLogo, alt: 'Shopify' },
  { src: airbnbLogo, alt: 'Airbnb' },
  { src: urbanOutfittersLogo, alt: 'Urban Outfitters' },
];

const PartnersSection = () => {
  return (
    <section className={styles.partnersSection}>
      <div className={styles.logoGrid}>
        {logos.map((logo, index) => (
          <img key={index} src={logo.src} alt={logo.alt} className={styles.logo} />
        ))}
      </div>
    </section>
  );
};

export default PartnersSection;