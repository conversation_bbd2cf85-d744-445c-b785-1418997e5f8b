/* src/features/PartnersSection/PartnersSection.module.css */
.partnersSection {
  background-color: white;
  padding: 4rem 5rem; 
}

.logoGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem 4rem; /
  align-items: center;
  justify-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  max-height: 40px; 
  max-width: 150px;
  filter: grayscale(100%) contrast(0%) brightness(120%);
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.logo:hover {
  opacity: 1;
}
